@import url('https://fonts.googleapis.com/css2?family=Fira+Sans:wght@300&display=swap');

 html, body {
     height: 100%;
}

 body {
     font-family: 'Fira Sans', cursive;
     font-size: 16pt;
     color: #020202;
     background-color: #f4da55;
}
 p {
     text-align: center;
     margin: 0 auto;
}
 .main-logo {
     display: flex;
     min-width: 180px;
     width: 180px;
     margin: 0 auto;
     padding-top: 2rem;
     margin-bottom: 1.5rem;
     
}
 .login-form {
     display: flex;
     min-width: 400px;
     width: 400px;
     margin: 0 auto;
     margin-top: 20px;
     flex-direction: column;
}
 .login-form>input {
     margin-bottom: 1.5rem;
}
 .submit-btns {
     width: 100%;
     text-align: center;
}
 .submit-btns>button {
     width: 45%;
}
 .submit-btn.is-primary {
     background-color: #ffeaa7 !important;
     border-color: #ef7385 !important;
     color: black;
}

.submit-btn.is-primary:hover {
    color: black;
}
 .submit-btn.is-primary::after {
     box-shadow:inset -4px -4px #ff6c52 !important;
}
 .submit-btn.is-primary:hover::after {
     box-shadow:inset -6px -6px #ff6c52 !important;
}
 .submit-btn.is-primary:focus {
     box-shadow:0 0 0 3px #494d7e !important;
}


/* query-msg */

 #resp-msg {
     display: none;
     margin-top: 2rem;
}
 .alert {
     position: relative;
     padding: .50rem 1.25rem;
     margin-bottom: 1rem;
     border: 1px solid transparent;
     border-radius: .25rem;
}
 .alert-success {
     color: #155724;
     background-color: #d4edda;
     border-color: #c3e6cb;
}
 .alert-danger {
     color: #721c24;
     background-color: #f8d7da;
     border-color: #f5c6cb;
}
 .alert-info {
     color: #0c5460;
     background-color: #d1ecf1;
     border-color: #bee5eb;
}


input[type=text], input[type=password] {
    border-color: #ff6c52 !important;
}