let mysql = require('mysql')

class Database {

    constructor() {
        this.connection = mysql.createConnection({
            host: 'localhost',
            user: 'root',
            password: 'rh0x01',
            database: 'spiky_tamagotchi'
        });
    }

    async registerUser(user, pass) {
		return new Promise(async (resolve, reject) => {
            let stmt = 'INSERT INTO users (username, password) VALUES (?, ?)';
            this.connection.query(stmt, [user, pass], (err, result) => {
                if(err)
                    reject(err)
                resolve(result)
            })
		});
	}

    async loginUser(user, pass) {
		return new Promise(async (resolve, reject) => {
			let stmt = 'SELECT username FROM users WHERE username = ? AND password = ?';
            this.connection.query(stmt, [user, pass], (err, result) => {
                if(err || result.length == 0)
                    reject(err)
                resolve(result)
            })
		});
	}

}

module.exports = Database;